import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Modal,
  message,
  Popconfirm,
  Typography,
  Tooltip,
  Row,
  Col,
  Alert,
  Spin,
  Timeline,
  Descriptions
} from 'antd';
import {
  HistoryOutlined,
  EyeOutlined,
  RollbackOutlined,
  SwapOutlined,
  InfoCircleOutlined,
  UserOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';
import templateService from '../../services/templateService';
import TemplatePreview from './TemplatePreview';

const { Title, Text } = Typography;

const TemplateVersionHistory = ({
  templateId,
  currentVersion,
  onVersionChange,
  visible,
  onClose
}) => {
  const [versions, setVersions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [compareVisible, setCompareVisible] = useState(false);
  const [selectedVersion, setSelectedVersion] = useState(null);
  const [compareVersions, setCompareVersions] = useState({ a: null, b: null });
  const [comparisonResult, setComparisonResult] = useState(null);
  const [rollbackLoading, setRollbackLoading] = useState(false);

  useEffect(() => {
    if (visible && templateId) {
      fetchVersions();
    }
  }, [visible, templateId]);

  const fetchVersions = async () => {
    setLoading(true);
    try {
      const response = await templateService.getTemplateVersions(templateId);
      setVersions(response.items || []);
    } catch (error) {
      message.error('获取版本历史失败');
    } finally {
      setLoading(false);
    }
  };

  const handlePreviewVersion = (version) => {
    setSelectedVersion(version);
    setPreviewVisible(true);
  };

  const handleCompareVersions = async (versionA, versionB) => {
    if (!versionA || !versionB) {
      message.warning('请选择两个版本进行比较');
      return;
    }

    setLoading(true);
    try {
      const result = await templateService.compareTemplateVersions(
        templateId,
        versionA.version,
        versionB.version
      );
      setComparisonResult(result);
      setCompareVersions({ a: versionA, b: versionB });
      setCompareVisible(true);
    } catch (error) {
      message.error('版本比较失败');
    } finally {
      setLoading(false);
    }
  };

  const handleRollback = async (targetVersion) => {
    setRollbackLoading(true);
    try {
      await templateService.rollbackTemplateToVersion(
        templateId,
        targetVersion.version,
        `回滚到版本 ${targetVersion.version}`
      );
      message.success('回滚成功');
      fetchVersions();
      if (onVersionChange) {
        onVersionChange();
      }
    } catch (error) {
      message.error('回滚失败');
    } finally {
      setRollbackLoading(false);
    }
  };

  const columns = [
    {
      title: '版本',
      dataIndex: 'version',
      key: 'version',
      width: 80,
      render: (version) => (
        <Tag color={version === currentVersion ? 'green' : 'blue'}>
          v{version}
          {version === currentVersion && <span style={{ marginLeft: 4 }}>(当前)</span>}
        </Tag>
      ),
    },
    {
      title: '变更说明',
      dataIndex: 'change_description',
      key: 'change_description',
      render: (description) => description || '无说明',
    },
    {
      title: '创建者',
      dataIndex: 'creator',
      key: 'creator',
      width: 120,
      render: (creator) => (
        <Space>
          <UserOutlined />
          <span>{creator?.name || creator?.username || '未知'}</span>
        </Space>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 160,
      render: (time) => (
        <Space>
          <ClockCircleOutlined />
          <span>{new Date(time).toLocaleString()}</span>
        </Space>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="预览版本">
            <Button
              size="small"
              icon={<EyeOutlined />}
              onClick={() => handlePreviewVersion(record)}
            />
          </Tooltip>

          {record.version !== currentVersion && (
            <Popconfirm
              title={`确定要回滚到版本 ${record.version} 吗？`}
              onConfirm={() => handleRollback(record)}
              okText="确定"
              cancelText="取消"
            >
              <Tooltip title="回滚到此版本">
                <Button
                  size="small"
                  icon={<RollbackOutlined />}
                  loading={rollbackLoading}
                />
              </Tooltip>
            </Popconfirm>
          )}

          <Tooltip title="与当前版本比较">
            <Button
              size="small"
              icon={<SwapOutlined />}
              onClick={() => {
                const currentVersionObj = versions.find(v => v.version === currentVersion);
                if (currentVersionObj) {
                  handleCompareVersions(record, currentVersionObj);
                }
              }}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  const renderComparisonModal = () => (
    <Modal
      title={
        <Space>
          <SwapOutlined />
          版本比较: v{compareVersions.a?.version} vs v{compareVersions.b?.version}
        </Space>
      }
      open={compareVisible}
      onCancel={() => setCompareVisible(false)}
      footer={[
        <Button key="close" onClick={() => setCompareVisible(false)}>
          关闭
        </Button>
      ]}
      width={800}
    >
      {comparisonResult && (
        <div>
          <Row gutter={16} style={{ marginBottom: 16 }}>
            <Col span={12}>
              <Card size="small" title={`版本 ${compareVersions.a?.version}`}>
                <Text type="secondary">
                  {new Date(compareVersions.a?.created_at).toLocaleString()}
                </Text>
                <br />
                <Text>{compareVersions.a?.change_description || '无说明'}</Text>
              </Card>
            </Col>
            <Col span={12}>
              <Card size="small" title={`版本 ${compareVersions.b?.version}`}>
                <Text type="secondary">
                  {new Date(compareVersions.b?.created_at).toLocaleString()}
                </Text>
                <br />
                <Text>{compareVersions.b?.change_description || '无说明'}</Text>
              </Card>
            </Col>
          </Row>

          <Card title="差异详情" size="small">
            {comparisonResult.differences && comparisonResult.differences.length > 0 ? (
              <Timeline>
                {comparisonResult.differences.map((diff, index) => (
                  <Timeline.Item
                    key={index}
                    color={getDiffColor(diff.type)}
                    dot={getDiffIcon(diff.type)}
                  >
                    <div>
                      <Text strong>{diff.path}</Text>
                      <br />
                      <Text type="secondary">{getDiffDescription(diff)}</Text>
                    </div>
                  </Timeline.Item>
                ))}
              </Timeline>
            ) : (
              <Alert message="两个版本内容相同" type="info" />
            )}
          </Card>
        </div>
      )}
    </Modal>
  );

  const getDiffColor = (type) => {
    const colors = {
      added: 'green',
      removed: 'red',
      modified: 'orange',
      type_change: 'purple'
    };
    return colors[type] || 'blue';
  };

  const getDiffIcon = (type) => {
    const icons = {
      added: '+',
      removed: '-',
      modified: '~',
      type_change: '!'
    };
    return icons[type] || '?';
  };

  const getDiffDescription = (diff) => {
    switch (diff.type) {
      case 'added':
        return `新增: ${JSON.stringify(diff.new_value)}`;
      case 'removed':
        return `删除: ${JSON.stringify(diff.old_value)}`;
      case 'modified':
        return `修改: ${JSON.stringify(diff.old_value)} → ${JSON.stringify(diff.new_value)}`;
      case 'type_change':
        return `类型变更: ${diff.old_type} → ${diff.new_type}`;
      default:
        return '未知变更';
    }
  };

  return (
    <>
      <Modal
        title={
          <Space>
            <HistoryOutlined />
            版本历史
          </Space>
        }
        open={visible}
        onCancel={onClose}
        footer={[
          <Button key="close" onClick={onClose}>
            关闭
          </Button>
        ]}
        width={1000}
      >
        <Spin spinning={loading}>
          <Alert
            message="版本管理说明"
            description="每次修改模板内容都会创建新版本。您可以预览历史版本、比较版本差异，或回滚到任意历史版本。"
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />

          <Table
            columns={columns}
            dataSource={versions}
            rowKey="id"
            pagination={{
              pageSize: 10,
              showSizeChanger: false,
              showQuickJumper: true,
            }}
            size="small"
          />
        </Spin>
      </Modal>

      {/* 版本预览模态框 */}
      <Modal
        title={`版本预览 - v${selectedVersion?.version}`}
        open={previewVisible}
        onCancel={() => setPreviewVisible(false)}
        footer={[
          <Button key="close" onClick={() => setPreviewVisible(false)}>
            关闭
          </Button>
        ]}
        width={800}
      >
        {selectedVersion && (
          <TemplatePreview
            template={{
              ...selectedVersion,
              content: selectedVersion.content
            }}
            showHeader={false}
            showActions={true}
          />
        )}
      </Modal>

      {/* 版本比较模态框 */}
      {renderComparisonModal()}
    </>
  );
};

export default TemplateVersionHistory;
