import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import {
  Layout,
  Typography,
  Table,
  Button,
  Space,
  Card,
  Input,
  Select,
  Tag,
  Modal,
  message,
  Tooltip,
  Row,
  Col,
  Dropdown,
  Popconfirm,
  Tabs
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  CopyOutlined,
  EyeOutlined,
  SearchOutlined,
  FilterOutlined,
  MoreOutlined,
  FileTextOutlined,
  HistoryOutlined,
  FormOutlined,
  BarChartOutlined
} from '@ant-design/icons';

// 导入Redux相关
import {
  fetchTemplates,
  deleteTemplate,
  copyTemplate,
  setFilters,
  resetFilters,
  setPagination,
  clearError,
  selectTemplates,
  selectTemplateLoading,
  selectTemplateError,
  selectTemplatePagination,
  selectTemplateFilters,
  selectTemplateOperations
} from '../store/slices/templateSlice';

// 导入组件
import TemplatePreview from '../components/template/TemplatePreview';
import TemplateVersionHistory from '../components/template/TemplateVersionHistory';
import TemplateStats from '../components/TemplateStats';

// 导入样式
import '../styles/TemplateManagement.css';

const { Content } = Layout;
const { Title } = Typography;
const { Option } = Select;
const { Search } = Input;

const TemplateManagementPage = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  // Redux状态
  const templates = useSelector(selectTemplates);
  const loading = useSelector(selectTemplateLoading);
  const error = useSelector(selectTemplateError);
  const pagination = useSelector(selectTemplatePagination);
  const filters = useSelector(selectTemplateFilters);
  const operations = useSelector(selectTemplateOperations);

  // 获取当前用户信息（用于权限控制）
  const { user: currentUser } = useSelector((state) => state.auth);

  // 本地状态
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [versionHistoryVisible, setVersionHistoryVisible] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [activeTab, setActiveTab] = useState('list');

  // 组件挂载时获取模板列表
  useEffect(() => {
    dispatch(fetchTemplates({
      page: pagination.page,
      size: pagination.size,
      ...filters
    }));
  }, [dispatch, pagination.page, pagination.size]);

  // 错误处理
  useEffect(() => {
    if (error) {
      message.error(error);
      dispatch(clearError());
    }
  }, [error, dispatch]);

  // 权限检查函数
  const canCreateTemplate = () => {
    return currentUser?.level >= 1; // 所有用户都可以创建模板
  };

  const canEditTemplate = (template) => {
    if (currentUser?.level >= 4) return true; // 总部管理员
    if (currentUser?.level >= 3 && template.company_id === currentUser.company_id) return true; // 分公司负责人
    return template.creator_id === currentUser?.id; // 创建者
  };

  const canDeleteTemplate = (template) => {
    if (currentUser?.level >= 4) return true; // 总部管理员
    if (currentUser?.level >= 3 && template.company_id === currentUser.company_id) return true; // 分公司负责人
    return template.creator_id === currentUser?.id; // 创建者
  };

  // 处理搜索
  const handleSearch = (value) => {
    dispatch(setFilters({ name: value }));
    dispatch(fetchTemplates({
      page: 1,
      size: pagination.size,
      ...filters,
      name: value
    }));
  };

  // 处理筛选
  const handleFilterChange = (key, value) => {
    const newFilters = { ...filters, [key]: value };
    dispatch(setFilters(newFilters));
    dispatch(fetchTemplates({
      page: 1,
      size: pagination.size,
      ...newFilters
    }));
  };

  // 重置筛选
  const handleResetFilters = () => {
    dispatch(resetFilters());
    dispatch(fetchTemplates({
      page: 1,
      size: pagination.size
    }));
  };

  // 处理分页
  const handleTableChange = (paginationConfig) => {
    const newPagination = {
      page: paginationConfig.current,
      size: paginationConfig.pageSize
    };
    dispatch(setPagination(newPagination));
    dispatch(fetchTemplates({
      ...newPagination,
      ...filters
    }));
  };

  // 处理新建模板
  const handleCreateTemplate = () => {
    navigate('/templates/create');
  };

  // 处理查看模板
  const handleViewTemplate = (template) => {
    navigate(`/templates/${template.id}`);
  };

  // 处理预览模板
  const handlePreviewTemplate = (template) => {
    setSelectedTemplate(template);
    setPreviewVisible(true);
  };

  // 处理版本历史
  const handleVersionHistory = (template) => {
    setSelectedTemplate(template);
    setVersionHistoryVisible(true);
  };

  // 版本变更回调
  const handleVersionChange = () => {
    // 重新获取模板列表以更新版本信息
    dispatch(fetchTemplates({
      page: pagination.page,
      size: pagination.size,
      ...filters
    }));
  };

  // 处理编辑模板
  const handleEditTemplate = (template) => {
    navigate(`/templates/${template.id}/edit`);
  };

  // 处理复制模板
  const handleCopyTemplate = (template) => {
    Modal.confirm({
      title: '复制模板',
      content: `确定要复制模板"${template.name}"吗？`,
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        dispatch(copyTemplate({
          id: template.id,
          newName: `${template.name} - 副本`
        }));
      }
    });
  };

  // 处理删除模板
  const handleDeleteTemplate = (template) => {
    dispatch(deleteTemplate(template.id));
  };

  // 批量删除
  const handleBatchDelete = () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要删除的模板');
      return;
    }

    Modal.confirm({
      title: '批量删除模板',
      content: `确定要删除选中的 ${selectedRowKeys.length} 个模板吗？此操作不可恢复。`,
      okText: '确定',
      cancelText: '取消',
      okType: 'danger',
      onOk: () => {
        selectedRowKeys.forEach(id => {
          dispatch(deleteTemplate(id));
        });
        setSelectedRowKeys([]);
      }
    });
  };

  // 表格列定义
  const columns = [
    {
      title: '模板名称',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <Button
          type="link"
          icon={<FileTextOutlined />}
          onClick={() => handleViewTemplate(record)}
          className="p-0"
        >
          {text}
        </Button>
      ),
      sorter: true,
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 100,
      render: (type) => {
        const typeConfig = {
          inquiry: { color: 'blue', text: '询价' },
          task: { color: 'green', text: '任务' },
          field: { color: 'orange', text: '字段' }
        };
        const config = typeConfig[type] || { color: 'default', text: type };
        return <Tag color={config.color}>{config.text}</Tag>;
      },
      filters: [
        { text: '询价', value: 'inquiry' },
        { text: '任务', value: 'task' },
        { text: '字段', value: 'field' }
      ],
    },
    {
      title: '分类',
      dataIndex: 'category',
      key: 'category',
      width: 120,
      render: (category) => category || '-',
    },
    {
      title: '范围',
      dataIndex: 'scope',
      key: 'scope',
      width: 100,
      render: (scope) => {
        const scopeConfig = {
          private: { color: 'red', text: '私有' },
          department: { color: 'orange', text: '部门' },
          company: { color: 'blue', text: '公司' },
          public: { color: 'green', text: '公开' }
        };
        const config = scopeConfig[scope] || { color: 'default', text: scope };
        return <Tag color={config.color}>{config.text}</Tag>;
      },
      filters: [
        { text: '私有', value: 'private' },
        { text: '部门', value: 'department' },
        { text: '公司', value: 'company' },
        { text: '公开', value: 'public' }
      ],
    },
    {
      title: '版本',
      dataIndex: 'version',
      key: 'version',
      width: 80,
      render: (version) => `v${version}`,
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 120,
      render: (date) => new Date(date).toLocaleDateString(),
      sorter: true,
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_, record) => {
        const items = [
          {
            key: 'view',
            label: '查看详情',
            icon: <EyeOutlined />,
            onClick: () => handleViewTemplate(record)
          },
          {
            key: 'preview',
            label: '预览模板',
            icon: <FormOutlined />,
            onClick: () => handlePreviewTemplate(record)
          },
          {
            key: 'versions',
            label: '版本历史',
            icon: <HistoryOutlined />,
            onClick: () => handleVersionHistory(record)
          },
          {
            key: 'copy',
            label: '复制模板',
            icon: <CopyOutlined />,
            onClick: () => handleCopyTemplate(record)
          }
        ];

        if (canEditTemplate(record)) {
          items.unshift({
            key: 'edit',
            label: '编辑',
            icon: <EditOutlined />,
            onClick: () => handleEditTemplate(record)
          });
        }

        if (canDeleteTemplate(record)) {
          items.push({
            key: 'delete',
            label: '删除',
            icon: <DeleteOutlined />,
            danger: true,
            onClick: () => handleDeleteTemplate(record)
          });
        }

        return (
          <Space size="small">
            <Button
              type="primary"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => handleViewTemplate(record)}
            >
              查看
            </Button>

            {canEditTemplate(record) && (
              <Button
                size="small"
                icon={<EditOutlined />}
                onClick={() => handleEditTemplate(record)}
              >
                编辑
              </Button>
            )}

            <Dropdown
              menu={{
                items,
                onClick: ({ key }) => {
                  const item = items.find(i => i.key === key);
                  if (item?.onClick) item.onClick();
                }
              }}
              trigger={['click']}
            >
              <Button size="small" icon={<MoreOutlined />} />
            </Dropdown>
          </Space>
        );
      },
    },
  ];

  // 行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: setSelectedRowKeys,
    getCheckboxProps: (record) => ({
      disabled: !canDeleteTemplate(record),
    }),
  };

  return (
    <Content className="p-6">
      {/* 页面标题和操作按钮 */}
      <div className="flex justify-between items-center mb-6">
        <Title level={2} className="m-0">
          <FileTextOutlined className="mr-2" />
          模板管理
        </Title>

        <Space>
          {selectedRowKeys.length > 0 && (
            <Popconfirm
              title={`确定要删除选中的 ${selectedRowKeys.length} 个模板吗？`}
              onConfirm={handleBatchDelete}
              okText="确定"
              cancelText="取消"
            >
              <Button
                danger
                icon={<DeleteOutlined />}
                loading={operations.deleting}
              >
                批量删除 ({selectedRowKeys.length})
              </Button>
            </Popconfirm>
          )}

          {canCreateTemplate() && (
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleCreateTemplate}
              loading={operations.creating}
            >
              新建模板
            </Button>
          )}
        </Space>
      </div>

      <Card>
        {/* 标签页 */}
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={[
            {
              key: 'list',
              label: (
                <span>
                  <FileTextOutlined />
                  模板列表
                </span>
              ),
              children: (
                <div>
                  {/* 搜索和筛选区域 */}
                  <Row gutter={16} className="mb-4">
          <Col xs={24} sm={12} md={8} lg={6}>
            <Search
              placeholder="搜索模板名称"
              allowClear
              enterButton={<SearchOutlined />}
              onSearch={handleSearch}
              defaultValue={filters.name}
            />
          </Col>

          <Col xs={12} sm={6} md={4} lg={3}>
            <Select
              placeholder="类型"
              allowClear
              style={{ width: '100%' }}
              value={filters.type}
              onChange={(value) => handleFilterChange('type', value)}
            >
              <Option value="inquiry">询价</Option>
              <Option value="task">任务</Option>
              <Option value="field">字段</Option>
            </Select>
          </Col>

          <Col xs={12} sm={6} md={4} lg={3}>
            <Select
              placeholder="范围"
              allowClear
              style={{ width: '100%' }}
              value={filters.scope}
              onChange={(value) => handleFilterChange('scope', value)}
            >
              <Option value="private">私有</Option>
              <Option value="department">部门</Option>
              <Option value="company">公司</Option>
              <Option value="public">公开</Option>
            </Select>
          </Col>

          <Col xs={24} sm={12} md={8} lg={6}>
            <Input
              placeholder="分类筛选"
              allowClear
              value={filters.category}
              onChange={(e) => handleFilterChange('category', e.target.value)}
            />
          </Col>

          <Col xs={24} sm={12} md={4} lg={3}>
            <Button
              icon={<FilterOutlined />}
              onClick={handleResetFilters}
              title="重置筛选"
            >
              重置
            </Button>
          </Col>
        </Row>

        {/* 模板列表表格 */}
        <Table
          columns={columns}
          dataSource={templates}
          rowKey="id"
          loading={loading}
          rowSelection={rowSelection}
          pagination={{
            current: pagination.page,
            pageSize: pagination.size,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
            pageSizeOptions: ['10', '20', '50', '100'],
          }}
          onChange={handleTableChange}
          scroll={{ x: 1200 }}
          size="middle"
        />
                </div>
              ),
            },
            {
              key: 'stats',
              label: (
                <span>
                  <BarChartOutlined />
                  统计分析
                </span>
              ),
              children: <TemplateStats />,
            },
          ]}
        />
      </Card>

      {/* 模板预览模态框 */}
      <Modal
        title="模板预览"
        open={previewVisible}
        onCancel={() => setPreviewVisible(false)}
        footer={[
          <Button key="close" onClick={() => setPreviewVisible(false)}>
            关闭
          </Button>
        ]}
        width={1000}
      >
        {selectedTemplate && (
          <TemplatePreview
            template={selectedTemplate}
            showHeader={false}
            showActions={true}
          />
        )}
      </Modal>

      {/* 版本历史模态框 */}
      <TemplateVersionHistory
        templateId={selectedTemplate?.id}
        currentVersion={selectedTemplate?.version}
        visible={versionHistoryVisible}
        onClose={() => setVersionHistoryVisible(false)}
        onVersionChange={handleVersionChange}
      />
    </Content>
  );
};

export default TemplateManagementPage;
