from sqlalchemy import <PERSON><PERSON><PERSON>, Column, DateTime, ForeignKey, String
from sqlalchemy.orm import relationship
import uuid

from app.models.base import BaseModel
from app.db.custom_types import UUID, JSON

class Task(BaseModel):
    """任务模型"""

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    title = Column(String, nullable=False)
    description = Column(String)
    creator_id = Column(UUID(as_uuid=True), ForeignKey("user.id"), nullable=False)
    company_id = Column(UUID(as_uuid=True), ForeignKey("company.id"))
    fields = Column(JSON, nullable=False)
    deadline = Column(DateTime(timezone=True))
    allow_guest = Column(Boolean, default=True)
    status = Column(String, default="draft")  # draft, active, closed
    publish_time = Column(DateTime(timezone=True))  # 任务发布时间

    # 关系
    creator = relationship("User", back_populates="tasks")
    company = relationship("Company", back_populates="tasks")
    quotes = relationship("Quote", back_populates="task")
