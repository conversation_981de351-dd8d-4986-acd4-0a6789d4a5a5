import React, { useEffect, useState } from 'react';
import { Layout, Typography, Table, Button, Space, Card, Input, Modal, Form, message, Switch, Tag, Tooltip } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, SettingOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { useSelector } from 'react-redux';
import api from '../services/api';

const { Content } = Layout;
const { Title } = Typography;
const { TextArea } = Input;

const SystemConfigPage = () => {
  const { user: currentUser } = useSelector((state) => state.auth);
  const [configs, setConfigs] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingConfig, setEditingConfig] = useState(null);
  const [form] = Form.useForm();
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [filteredConfigs, setFilteredConfigs] = useState([]);

  // 预定义的配置模板
  const configTemplates = {
    'supplier_need_verify': {
      description: '是否需要审核新注册的供应商。启用后，新注册的供应商需要管理员审核后才能参与报价',
      defaultValue: { enabled: false },
      valueType: 'boolean'
    },
    'user_need_verify': {
      description: '是否需要审核新注册的用户。启用后，新注册的用户需要管理员激活后才能登录',
      defaultValue: { enabled: false },
      valueType: 'boolean'
    },
    'max_quote_amount': {
      description: '单次报价的最大金额限制（元）',
      defaultValue: { amount: 1000000 },
      valueType: 'number'
    },
    'system_maintenance': {
      description: '系统维护模式。启用后，普通用户将无法访问系统',
      defaultValue: { enabled: false, message: '系统正在维护中，请稍后再试' },
      valueType: 'object'
    },
    'template_auto_save': {
      description: '是否启用模板自动保存功能。启用后，用户创建任务时会自动提示保存为模板',
      defaultValue: { enabled: true },
      valueType: 'boolean'
    },
    'template_public_create': {
      description: '是否允许普通用户创建公开模板。禁用后，只有管理员可以创建公开模板',
      defaultValue: { enabled: false },
      valueType: 'boolean'
    },
    'template_max_count': {
      description: '每个用户最大模板数量限制',
      defaultValue: { count: 50 },
      valueType: 'number'
    },
    'template_version_limit': {
      description: '模板版本历史保留数量限制',
      defaultValue: { limit: 10 },
      valueType: 'number'
    }
  };

  // 获取配置列表
  const fetchConfigs = async () => {
    try {
      setLoading(true);
      const response = await api.get('/system-config');
      console.log('获取系统配置列表响应:', response);
      setConfigs(response);
      setFilteredConfigs(response);
    } catch (error) {
      console.error('获取系统配置列表失败:', error);
      message.error(error.response?.data?.detail || '获取系统配置列表失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchConfigs();
  }, []);

  // 搜索配置
  useEffect(() => {
    if (searchText) {
      const filtered = configs.filter(
        config =>
          config.key.toLowerCase().includes(searchText.toLowerCase()) ||
          (config.description && config.description.toLowerCase().includes(searchText.toLowerCase()))
      );
      setFilteredConfigs(filtered);
    } else {
      setFilteredConfigs(configs);
    }
  }, [searchText, configs]);

  // 打开创建配置模态框
  const showCreateModal = () => {
    setEditingConfig(null);
    form.resetFields();
    setModalVisible(true);
  };

  // 打开编辑配置模态框
  const showEditModal = (config) => {
    setEditingConfig(config);
    form.setFieldsValue({
      key: config.key,
      description: config.description,
      value: JSON.stringify(config.value, null, 2)
    });
    setModalVisible(true);
  };

  // 关闭模态框
  const handleCancel = () => {
    setModalVisible(false);
  };

  // 提交表单
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      console.log('提交表单数据:', values);
      setConfirmLoading(true);

      // 解析JSON值
      let parsedValue;
      try {
        parsedValue = JSON.parse(values.value);
      } catch (error) {
        message.error('配置值必须是有效的JSON格式');
        return;
      }

      const configData = {
        key: values.key,
        value: parsedValue,
        description: values.description
      };

      if (editingConfig) {
        // 更新配置
        const updateResponse = await api.put(`/system-config/${editingConfig.key}`, {
          value: parsedValue,
          description: values.description
        });
        console.log('更新配置响应:', updateResponse);
        message.success('配置更新成功');
      } else {
        // 创建配置
        const createResponse = await api.post('/system-config', configData);
        console.log('创建配置响应:', createResponse);
        message.success('配置创建成功');
      }

      setModalVisible(false);
      setTimeout(() => {
        fetchConfigs();
      }, 500);
    } catch (error) {
      console.error('表单提交错误:', error);
      message.error(error.response?.data?.detail || '操作失败，请稍后再试');
    } finally {
      setConfirmLoading(false);
    }
  };

  // 删除配置
  const handleDelete = async (configKey) => {
    Modal.confirm({
      title: '确认删除配置',
      content: (
        <div>
          <p>⚠️ <strong>警告：此操作将永久删除配置项！</strong></p>
          <p>• 配置项 <code>{configKey}</code> 将从系统中完全删除</p>
          <p>• 依赖此配置的功能可能会受到影响</p>
          <p>• 此操作不可逆，无法恢复</p>
          <p style={{ marginTop: '12px' }}>确定要继续吗？</p>
        </div>
      ),
      okText: '确认删除',
      cancelText: '取消',
      okType: 'danger',
      onOk: async () => {
        try {
          await api.delete(`/system-config/${configKey}`);
          message.success('配置已删除');
          fetchConfigs();
        } catch (error) {
          message.error(error.response?.data?.detail || '删除失败，请稍后再试');
        }
      }
    });
  };

  // 快速切换布尔值配置
  const handleQuickToggle = async (config) => {
    if (config.value && typeof config.value === 'object' && 'enabled' in config.value) {
      try {
        const newValue = { ...config.value, enabled: !config.value.enabled };
        await api.put(`/system-config/${config.key}`, {
          value: newValue,
          description: config.description
        });
        message.success(`${config.key} 已${newValue.enabled ? '启用' : '禁用'}`);
        fetchConfigs();
      } catch (error) {
        message.error(error.response?.data?.detail || '操作失败');
      }
    }
  };

  // 使用模板填充表单
  const useTemplate = (templateKey) => {
    const template = configTemplates[templateKey];
    if (template) {
      form.setFieldsValue({
        key: templateKey,
        description: template.description,
        value: JSON.stringify(template.defaultValue, null, 2)
      });
    }
  };

  // 渲染配置值
  const renderConfigValue = (value, config) => {
    if (typeof value === 'object' && value !== null) {
      if ('enabled' in value) {
        return (
          <Space>
            <Switch
              checked={value.enabled}
              onChange={() => handleQuickToggle(config)}
              size="small"
            />
            <span>{value.enabled ? '启用' : '禁用'}</span>
            {Object.keys(value).length > 1 && (
              <Tooltip title={JSON.stringify(value, null, 2)}>
                <InfoCircleOutlined style={{ color: '#1890ff' }} />
              </Tooltip>
            )}
          </Space>
        );
      }
      return (
        <Tooltip title={JSON.stringify(value, null, 2)}>
          <Tag color="blue">JSON对象</Tag>
        </Tooltip>
      );
    }
    return String(value);
  };

  // 表格列定义
  const columns = [
    {
      title: '配置键',
      dataIndex: 'key',
      key: 'key',
      width: 200,
      render: (key) => <code style={{ backgroundColor: '#f5f5f5', padding: '2px 6px', borderRadius: '3px' }}>{key}</code>
    },
    {
      title: '配置值',
      dataIndex: 'value',
      key: 'value',
      width: 250,
      render: (value, record) => renderConfigValue(value, record)
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      render: (text) => text || '-',
    },
    {
      title: '更新时间',
      dataIndex: 'updated_at',
      key: 'updated_at',
      width: 180,
      render: (time) => time ? new Date(time).toLocaleString() : '-'
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_, record) => (
        <Space size="middle">
          <Button
            type="primary"
            icon={<EditOutlined />}
            size="small"
            onClick={() => showEditModal(record)}
          >
            编辑
          </Button>
          <Button
            danger
            icon={<DeleteOutlined />}
            size="small"
            onClick={() => handleDelete(record.key)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <Content className="p-6">
      <div className="flex justify-between items-center mb-4">
        <Title level={2}>
          <SettingOutlined className="mr-2" />
          系统配置管理
        </Title>
        <Button type="primary" icon={<PlusOutlined />} onClick={showCreateModal}>
          新增配置
        </Button>
      </div>

      <Card>
        <div className="mb-4">
          <Input.Search
            placeholder="搜索配置键或描述"
            allowClear
            enterButton
            onSearch={value => setSearchText(value)}
            style={{ width: 300 }}
          />
        </div>

        <Table
          columns={columns}
          dataSource={filteredConfigs}
          rowKey="key"
          loading={loading}
          pagination={{ pageSize: 10 }}
          scroll={{ x: 1000 }}
        />
      </Card>

      {/* 创建/编辑配置模态框 */}
      <Modal
        title={editingConfig ? '编辑系统配置' : '新增系统配置'}
        open={modalVisible}
        onOk={handleSubmit}
        onCancel={handleCancel}
        confirmLoading={confirmLoading}
        okText={editingConfig ? '更新' : '创建'}
        cancelText="取消"
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="key"
            label="配置键"
            rules={[{ required: true, message: '请输入配置键' }]}
          >
            <Input
              placeholder="配置键（如：supplier_need_verify）"
              disabled={!!editingConfig}
            />
          </Form.Item>

          <Form.Item
            name="description"
            label="描述"
            rules={[{ required: true, message: '请输入配置描述' }]}
          >
            <TextArea
              placeholder="配置项的详细描述"
              rows={2}
            />
          </Form.Item>

          <Form.Item
            name="value"
            label="配置值"
            rules={[
              { required: true, message: '请输入配置值' },
              {
                validator: (_, value) => {
                  try {
                    JSON.parse(value);
                    return Promise.resolve();
                  } catch (error) {
                    return Promise.reject(new Error('配置值必须是有效的JSON格式'));
                  }
                }
              }
            ]}
          >
            <TextArea
              placeholder='配置值（JSON格式，如：{"enabled": true}）'
              rows={4}
            />
          </Form.Item>

          {!editingConfig && (
            <div className="mb-4">
              <p className="text-gray-600 mb-2">常用配置模板：</p>
              <Space wrap>
                {Object.keys(configTemplates).map(key => (
                  <Button
                    key={key}
                    size="small"
                    onClick={() => useTemplate(key)}
                  >
                    {key}
                  </Button>
                ))}
              </Space>
            </div>
          )}
        </Form>
      </Modal>
    </Content>
  );
};

export default SystemConfigPage;
