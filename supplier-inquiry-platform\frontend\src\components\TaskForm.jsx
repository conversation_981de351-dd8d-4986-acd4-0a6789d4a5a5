import React, { useState, useEffect } from 'react';
import { Form, Input, DatePicker, Switch, Select, Button, Space, Card, Modal, Tag, Divider, message, Tooltip } from 'antd';
import { PlusOutlined, MinusCircleOutlined, UploadOutlined, DownloadOutlined, InfoCircleOutlined, SaveOutlined, FileTextOutlined } from '@ant-design/icons';
import { useSelector, useDispatch } from 'react-redux';
import moment from 'moment';
import ExcelFieldImport from './ExcelFieldImport';
import { fetchTemplates, createTemplate, selectTemplates, selectTemplateLoading } from '../store/slices/templateSlice';

const { TextArea } = Input;
const { Option } = Select;

// 定义主键字段名称列表
const PRIMARY_KEY_FIELDS = ["商品条码", "商品编号", "产品编号", "产品条码", "条码", "编号", "barcode", "product_code", "product_id"];

const TaskForm = ({ initialValues, onSubmit, loading }) => {
  const dispatch = useDispatch();
  const [form] = Form.useForm();
  const [fields, setFields] = useState([]);
  const [optionsModalVisible, setOptionsModalVisible] = useState(false);
  const [currentFieldIndex, setCurrentFieldIndex] = useState(null);
  const [options, setOptions] = useState([]);
  const [templateModalVisible, setTemplateModalVisible] = useState(false);
  const [saveTemplateModalVisible, setSaveTemplateModalVisible] = useState(false);
  const [templateForm] = Form.useForm();

  const templates = useSelector(selectTemplates);
  const templatesLoading = useSelector(selectTemplateLoading);
  const { user } = useSelector((state) => state.auth);

  // 加载模板列表
  useEffect(() => {
    dispatch(fetchTemplates());
  }, [dispatch]);

  useEffect(() => {
    if (initialValues) {
      // 转换日期格式
      const values = {
        ...initialValues,
        deadline: initialValues.deadline ? moment(initialValues.deadline) : null,
      };

      // 设置表单初始值
      form.setFieldsValue(values);

      // 设置自定义字段
      if (initialValues.fields) {
        const fieldArray = Object.entries(initialValues.fields).map(([key, value]) => {
          let type = 'string';
          let options = [];

          if (typeof value === 'boolean') {
            type = 'boolean';
          } else if (Array.isArray(value)) {
            type = 'array';
          } else if (typeof value === 'number') {
            type = 'number';
          } else if (value && value._isAMomentObject) {
            type = 'date';
          } else if (value && value._type === 'select') {
            type = 'select';
            options = value.options || [];
          } else if (value && value._type === 'multiselect') {
            type = 'multiselect';
            options = value.options || [];
          }

          return {
            name: key,
            type,
            required: false,
            options,
          };
        });
        setFields(fieldArray);
      }
    }
  }, [initialValues, form]);

  // 检查是否包含主键字段
  const checkHasPrimaryKeyField = (fields) => {
    if (!fields || fields.length === 0) return false;

    return fields.some(field =>
      PRIMARY_KEY_FIELDS.some(keyField =>
        field.name && field.name.toLowerCase().includes(keyField.toLowerCase())
      )
    );
  };

  const handleSubmit = (values) => {
    // 验证是否包含主键字段
    if (!checkHasPrimaryKeyField(values.fields)) {
      message.error('任务必须包含商品条码或商品编号等作为主键字段！');
      return;
    }

    // 转换字段格式
    const fieldsObject = {};
    values.fields?.forEach(field => {
      // 根据字段类型设置不同的初始值
      let initialValue = null;

      switch (field.type) {
        case 'boolean':
          initialValue = false;
          break;
        case 'number':
          initialValue = 0;
          break;
        case 'date':
          initialValue = null;
          break;
        case 'select':
          initialValue = { _type: 'select', value: null, options: field.options || [] };
          break;
        case 'multiselect':
          initialValue = { _type: 'multiselect', value: [], options: field.options || [] };
          break;
        case 'array':
          initialValue = [];
          break;
        default:
          initialValue = '';
      }

      fieldsObject[field.name] = initialValue;
    });

    // 提交表单
    onSubmit({
      ...values,
      fields: fieldsObject,
    });
  };

  // 打开选项配置对话框
  const openOptionsModal = (index) => {
    const field = form.getFieldValue(['fields', index]);
    setCurrentFieldIndex(index);
    setOptions(field.options || []);
    setOptionsModalVisible(true);
  };

  // 保存选项配置
  const saveOptions = () => {
    const fields = form.getFieldValue('fields');
    const newFields = [...fields];
    newFields[currentFieldIndex].options = options;
    form.setFieldsValue({ fields: newFields });
    setOptionsModalVisible(false);
  };

  // 添加选项
  const addOption = () => {
    setOptions([...options, { label: '', value: '' }]);
  };

  // 更新选项
  const updateOption = (index, key, value) => {
    const newOptions = [...options];
    newOptions[index][key] = value;
    setOptions(newOptions);
  };

  // 删除选项
  const removeOption = (index) => {
    const newOptions = [...options];
    newOptions.splice(index, 1);
    setOptions(newOptions);
  };

  // 添加默认主键字段
  const addPrimaryKeyField = () => {
    const currentFields = form.getFieldValue('fields') || [];
    const newField = {
      name: '商品条码',
      type: 'string',
      required: true
    };
    form.setFieldsValue({
      fields: [...currentFields, newField]
    });
  };

  // 使用模板
  const useTemplate = (template) => {
    try {
      const templateContent = JSON.parse(template.content);

      // 设置基本信息
      form.setFieldsValue({
        title: templateContent.title || '',
        description: templateContent.description || '',
        allow_guest: templateContent.allow_guest !== undefined ? templateContent.allow_guest : true,
        status: templateContent.status || 'draft',
      });

      // 设置自定义字段
      if (templateContent.fields) {
        const fieldArray = Object.entries(templateContent.fields).map(([key, value]) => {
          let type = 'string';
          let options = [];

          if (typeof value === 'boolean') {
            type = 'boolean';
          } else if (Array.isArray(value)) {
            type = 'array';
          } else if (typeof value === 'number') {
            type = 'number';
          } else if (value && value._type === 'select') {
            type = 'select';
            options = value.options || [];
          } else if (value && value._type === 'multiselect') {
            type = 'multiselect';
            options = value.options || [];
          }

          return {
            name: key,
            type,
            required: false,
            options,
          };
        });

        form.setFieldsValue({ fields: fieldArray });
      }

      setTemplateModalVisible(false);
      message.success(`已应用模板：${template.name}`);
    } catch (error) {
      console.error('应用模板失败:', error);
      message.error('模板格式错误，无法应用');
    }
  };

  // 保存为模板
  const saveAsTemplate = async (templateData) => {
    try {
      const formValues = form.getFieldsValue();

      // 转换字段格式
      const fieldsObject = {};
      formValues.fields?.forEach(field => {
        let initialValue = null;
        switch (field.type) {
          case 'boolean':
            initialValue = false;
            break;
          case 'number':
            initialValue = 0;
            break;
          case 'date':
            initialValue = null;
            break;
          case 'select':
            initialValue = { _type: 'select', value: null, options: field.options || [] };
            break;
          case 'multiselect':
            initialValue = { _type: 'multiselect', value: [], options: field.options || [] };
            break;
          case 'array':
            initialValue = [];
            break;
          default:
            initialValue = '';
        }
        fieldsObject[field.name] = initialValue;
      });

      const templateContent = {
        title: formValues.title || '',
        description: formValues.description || '',
        allow_guest: formValues.allow_guest,
        status: formValues.status,
        fields: fieldsObject,
      };

      // 调用保存模板的API
      const newTemplate = {
        ...templateData,
        content: JSON.stringify(templateContent),
        type: 'task',
        creator_id: user?.id,
        company_id: user?.company_id,
        is_active: true,
      };

      await dispatch(createTemplate(newTemplate)).unwrap();

      setSaveTemplateModalVisible(false);
      templateForm.resetFields();
      message.success('模板保存成功');

      // 重新加载模板列表
      dispatch(fetchTemplates());
    } catch (error) {
      console.error('保存模板失败:', error);
      message.error('保存模板失败');
    }
  };

  return (
    <Form
      form={form}
      layout="vertical"
      onFinish={handleSubmit}
      initialValues={{
        allow_guest: true,
        status: 'draft',
        fields: [],
      }}
    >
      {/* 模板操作区域 */}
      <Card size="small" style={{ marginBottom: 16, backgroundColor: '#fafafa' }}>
        <Space>
          <Tooltip title="从现有模板快速创建任务">
            <Button
              icon={<FileTextOutlined />}
              onClick={() => setTemplateModalVisible(true)}
              loading={templatesLoading}
            >
              使用模板
            </Button>
          </Tooltip>
          <Tooltip title="将当前表单保存为模板">
            <Button
              icon={<SaveOutlined />}
              onClick={() => setSaveTemplateModalVisible(true)}
            >
              保存为模板
            </Button>
          </Tooltip>
        </Space>
      </Card>

      <Form.Item
        name="title"
        label="任务标题"
        rules={[{ required: true, message: '请输入任务标题' }]}
      >
        <Input placeholder="请输入任务标题" />
      </Form.Item>

      <Form.Item
        name="description"
        label="任务描述"
      >
        <TextArea rows={4} placeholder="请输入任务描述" />
      </Form.Item>

      <Form.Item
        name="deadline"
        label="截止日期"
      >
        <DatePicker showTime format="YYYY-MM-DD HH:mm:ss" placeholder="选择截止日期" style={{ width: '100%' }} />
      </Form.Item>

      <Form.Item
        name="allow_guest"
        label="允许访客访问"
        valuePropName="checked"
      >
        <Switch />
      </Form.Item>

      <Form.Item
        name="status"
        label="任务状态"
      >
        <Select>
          <Option value="draft">草稿</Option>
          <Option value="active">活跃</Option>
          <Option value="closed">关闭</Option>
        </Select>
      </Form.Item>

      <Form.List name="fields">
        {(fields, { add, remove, setFields: setFormFields }) => (
          <>
            <Form.Item label="自定义字段">
              <Space direction="vertical" style={{ width: '100%' }}>
                <div style={{ display: 'flex', marginBottom: 16 }}>
                  <Button type="dashed" onClick={() => add()} icon={<PlusOutlined />} style={{ flex: 1, marginRight: 8 }}>
                  添加字段
                </Button>
                  <Button type="primary" onClick={addPrimaryKeyField} icon={<InfoCircleOutlined />}>
                    添加主键字段
                  </Button>
                </div>

                <div style={{
                  padding: '12px',
                  background: '#fffbe6',
                  border: '1px solid #ffe58f',
                  borderRadius: '2px',
                  marginBottom: '16px'
                }}>
                  <p style={{ margin: 0, color: '#d48806' }}>
                    <InfoCircleOutlined style={{ marginRight: 8 }} />
                    <strong>注意：</strong> 任务必须包含至少一个商品主键字段（如"商品条码"、"商品编号"），否则创建将失败
                  </p>
                </div>

                <Divider>或</Divider>
                <ExcelFieldImport
                  onImport={(importedFields) => {
                    // 清除现有字段
                    fields.forEach((field) => remove(field.name));

                    // 添加导入的字段
                    importedFields.forEach((field) => {
                      add(field);
                    });
                  }}
                />
              </Space>
            </Form.Item>
            {fields.map(field => (
              <Space key={field.key} style={{ display: 'flex', marginBottom: 8 }} align="baseline">
                <Form.Item
                  key={field.key}
                  name={[field.name, 'name']}
                  fieldKey={[field.fieldKey, 'name']}
                  rules={[{ required: true, message: '请输入字段名称' }]}
                >
                  <Input placeholder="字段名称" />
                </Form.Item>
                <Form.Item
                  key={field.key}
                  name={[field.name, 'type']}
                  fieldKey={[field.fieldKey, 'type']}
                  rules={[{ required: true, message: '请选择字段类型' }]}
                >
                  <Select
                    style={{ width: 120 }}
                    placeholder="字段类型"
                    onChange={(value) => {
                      // 当选择单选或多选类型时，自动打开选项配置
                      if (value === 'select' || value === 'multiselect') {
                        setTimeout(() => openOptionsModal(field.name), 100);
                      }
                    }}
                  >
                    <Option value="string">文本</Option>
                    <Option value="number">数字</Option>
                    <Option value="boolean">布尔值</Option>
                    <Option value="date">日期</Option>
                    <Option value="select">单选</Option>
                    <Option value="multiselect">多选</Option>
                    <Option value="array">列表</Option>
                  </Select>
                </Form.Item>
                <Form.Item
                  key={field.key}
                  name={[field.name, 'required']}
                  fieldKey={[field.fieldKey, 'required']}
                  valuePropName="checked"
                >
                  <Switch checkedChildren="必填" unCheckedChildren="选填" />
                </Form.Item>

                {/* 为单选和多选类型添加选项配置按钮 */}
                {form.getFieldValue(['fields', field.name, 'type']) === 'select' ||
                 form.getFieldValue(['fields', field.name, 'type']) === 'multiselect' ? (
                  <Button
                    type="link"
                    onClick={() => openOptionsModal(field.name)}
                    style={{ padding: '0 8px' }}
                  >
                    配置选项
                  </Button>
                ) : null}

                <MinusCircleOutlined onClick={() => remove(field.name)} />
              </Space>
            ))}
          </>
        )}
      </Form.List>

      <Form.Item>
        <Button type="primary" htmlType="submit" loading={loading}>
          {initialValues ? '更新' : '创建'}
        </Button>
      </Form.Item>

      {/* 选项配置对话框 */}
      <Modal
        title="配置选项"
        open={optionsModalVisible}
        onOk={saveOptions}
        onCancel={() => setOptionsModalVisible(false)}
      >
        <div style={{ marginBottom: 16 }}>
          <Button type="dashed" onClick={addOption} block icon={<PlusOutlined />}>
            添加选项
          </Button>
        </div>

        {options.map((option, index) => (
          <div key={`option-${index}-${option.label || option.value || Date.now()}`} style={{ display: 'flex', marginBottom: 8 }}>
            <Input
              placeholder="选项标签"
              value={option.label}
              onChange={(e) => updateOption(index, 'label', e.target.value)}
              style={{ marginRight: 8 }}
            />
            <Input
              placeholder="选项值"
              value={option.value}
              onChange={(e) => updateOption(index, 'value', e.target.value)}
              style={{ marginRight: 8 }}
            />
            <MinusCircleOutlined onClick={() => removeOption(index)} />
          </div>
        ))}
      </Modal>

      {/* 使用模板模态框 */}
      <Modal
        title="选择模板"
        open={templateModalVisible}
        onCancel={() => setTemplateModalVisible(false)}
        footer={null}
        width={800}
      >
        <div style={{ maxHeight: 400, overflowY: 'auto' }}>
          {templates && templates.length > 0 ? (
            <Space direction="vertical" style={{ width: '100%' }}>
              {templates
                .filter(template => template.type === 'task' && template.is_active)
                .map(template => (
                  <Card
                    key={template.id}
                    size="small"
                    hoverable
                    onClick={() => useTemplate(template)}
                    style={{ cursor: 'pointer' }}
                  >
                    <Card.Meta
                      title={template.name}
                      description={
                        <div>
                          <p>{template.description}</p>
                          <Space>
                            <Tag color="blue">{template.category}</Tag>
                            <Tag color="green">{template.scope}</Tag>
                            {template.tags && template.tags.map(tag => (
                              <Tag key={tag}>{tag}</Tag>
                            ))}
                          </Space>
                        </div>
                      }
                    />
                  </Card>
                ))}
            </Space>
          ) : (
            <div style={{ textAlign: 'center', padding: 40 }}>
              <p>暂无可用模板</p>
            </div>
          )}
        </div>
      </Modal>

      {/* 保存为模板模态框 */}
      <Modal
        title="保存为模板"
        open={saveTemplateModalVisible}
        onOk={() => templateForm.submit()}
        onCancel={() => setSaveTemplateModalVisible(false)}
        okText="保存"
        cancelText="取消"
      >
        <Form
          form={templateForm}
          layout="vertical"
          onFinish={saveAsTemplate}
        >
          <Form.Item
            name="name"
            label="模板名称"
            rules={[{ required: true, message: '请输入模板名称' }]}
          >
            <Input placeholder="请输入模板名称" />
          </Form.Item>

          <Form.Item
            name="description"
            label="模板描述"
          >
            <TextArea rows={3} placeholder="请输入模板描述" />
          </Form.Item>

          <Form.Item
            name="category"
            label="模板分类"
            rules={[{ required: true, message: '请选择模板分类' }]}
          >
            <Select placeholder="请选择模板分类">
              <Option value="采购">采购</Option>
              <Option value="询价">询价</Option>
              <Option value="招标">招标</Option>
              <Option value="其他">其他</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="scope"
            label="共享范围"
            rules={[{ required: true, message: '请选择共享范围' }]}
          >
            <Select placeholder="请选择共享范围">
              <Option value="private">私有</Option>
              <Option value="department">部门</Option>
              <Option value="company">公司</Option>
              {user?.level >= 4 && <Option value="public">公开</Option>}
            </Select>
          </Form.Item>

          <Form.Item
            name="tags"
            label="标签"
          >
            <Select
              mode="tags"
              placeholder="请输入标签"
              style={{ width: '100%' }}
            />
          </Form.Item>
        </Form>
      </Modal>
    </Form>
  );
};

export default TaskForm;
