from sqlalchemy import <PERSON><PERSON><PERSON>, Column, DateTime, ForeignKey, String
from sqlalchemy.orm import relationship
import uuid

from app.models.base import BaseModel
from app.db.custom_types import UUID, JSON

class Quote(BaseModel):
    """报价模型"""

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    task_id = Column(UUID(as_uuid=True), ForeignKey("task.id"), nullable=False)
    supplier_id = Column(UUID(as_uuid=True), ForeignKey("company.id"))
    supplier_name = Column(String, nullable=False)
    data = Column(JSON, nullable=False)
    attachments = Column(JSON)
    is_registered = Column(Boolean, default=False)
    created_by_id = Column(UUID(as_uuid=True), ForeignKey("user.id"))
    quote_time = Column(DateTime(timezone=True))  # 报价时间

    # 关系
    task = relationship("Task", back_populates="quotes")
    supplier = relationship("Company", back_populates="quotes")
    created_by = relationship("User", back_populates="quotes")
