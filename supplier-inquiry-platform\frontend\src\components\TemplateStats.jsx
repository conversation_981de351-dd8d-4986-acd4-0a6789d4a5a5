import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Statistic, Table, Tag, Progress, Tooltip } from 'antd';
import { FileTextOutlined, EyeOutlined, DownloadOutlined, ShareAltOutlined } from '@ant-design/icons';
import { useSelector, useDispatch } from 'react-redux';
import { getTemplates } from '../store/slices/templateSlice';

const TemplateStats = () => {
  const dispatch = useDispatch();
  const { templates, loading } = useSelector((state) => state.templates);
  const { user } = useSelector((state) => state.auth);
  const [stats, setStats] = useState({
    totalTemplates: 0,
    myTemplates: 0,
    publicTemplates: 0,
    mostUsedTemplates: [],
    categoryStats: [],
  });

  useEffect(() => {
    dispatch(getTemplates());
  }, [dispatch]);

  useEffect(() => {
    if (templates && templates.length > 0) {
      calculateStats();
    }
  }, [templates, user]);

  const calculateStats = () => {
    const totalTemplates = templates.length;
    const myTemplates = templates.filter(t => t.creator_id === user?.id).length;
    const publicTemplates = templates.filter(t => t.scope === 'public').length;

    // 按使用次数排序（模拟数据，实际应该从后端获取）
    const mostUsedTemplates = templates
      .map(template => ({
        ...template,
        usage_count: Math.floor(Math.random() * 100), // 模拟使用次数
      }))
      .sort((a, b) => b.usage_count - a.usage_count)
      .slice(0, 5);

    // 按分类统计
    const categoryMap = {};
    templates.forEach(template => {
      const category = template.category || '未分类';
      if (!categoryMap[category]) {
        categoryMap[category] = { count: 0, templates: [] };
      }
      categoryMap[category].count++;
      categoryMap[category].templates.push(template);
    });

    const categoryStats = Object.entries(categoryMap).map(([category, data]) => ({
      category,
      count: data.count,
      percentage: ((data.count / totalTemplates) * 100).toFixed(1),
    }));

    setStats({
      totalTemplates,
      myTemplates,
      publicTemplates,
      mostUsedTemplates,
      categoryStats,
    });
  };

  const columns = [
    {
      title: '模板名称',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <div>
          <div>{text}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {record.description}
          </div>
        </div>
      ),
    },
    {
      title: '分类',
      dataIndex: 'category',
      key: 'category',
      render: (category) => <Tag color="blue">{category}</Tag>,
    },
    {
      title: '使用次数',
      dataIndex: 'usage_count',
      key: 'usage_count',
      render: (count) => (
        <Statistic
          value={count}
          prefix={<EyeOutlined />}
          valueStyle={{ fontSize: '14px' }}
        />
      ),
    },
    {
      title: '共享范围',
      dataIndex: 'scope',
      key: 'scope',
      render: (scope) => {
        const scopeMap = {
          private: { color: 'default', text: '私有' },
          department: { color: 'orange', text: '部门' },
          company: { color: 'green', text: '公司' },
          public: { color: 'blue', text: '公开' },
        };
        const config = scopeMap[scope] || scopeMap.private;
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
  ];

  const categoryColumns = [
    {
      title: '分类',
      dataIndex: 'category',
      key: 'category',
    },
    {
      title: '模板数量',
      dataIndex: 'count',
      key: 'count',
    },
    {
      title: '占比',
      dataIndex: 'percentage',
      key: 'percentage',
      render: (percentage) => (
        <div style={{ width: 100 }}>
          <Progress percent={parseFloat(percentage)} size="small" />
        </div>
      ),
    },
  ];

  return (
    <div>
      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总模板数"
              value={stats.totalTemplates}
              prefix={<FileTextOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="我的模板"
              value={stats.myTemplates}
              prefix={<FileTextOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="公开模板"
              value={stats.publicTemplates}
              prefix={<ShareAltOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="使用率"
              value={stats.totalTemplates > 0 ? ((stats.myTemplates / stats.totalTemplates) * 100).toFixed(1) : 0}
              suffix="%"
              prefix={<EyeOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 最常用模板 */}
      <Row gutter={16}>
        <Col span={16}>
          <Card title="最常用模板" loading={loading}>
            <Table
              columns={columns}
              dataSource={stats.mostUsedTemplates}
              rowKey="id"
              pagination={false}
              size="small"
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card title="分类统计" loading={loading}>
            <Table
              columns={categoryColumns}
              dataSource={stats.categoryStats}
              rowKey="category"
              pagination={false}
              size="small"
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default TemplateStats;
