# 权限常量定义
# 模板相关权限
TEMPLATE_READ = "template_read"
TEMPLATE_CREATE = "template_create"
TEMPLATE_UPDATE = "template_update"
TEMPLATE_DELETE = "template_delete"
TEMPLATE_SHARE = "template_share"
TEMPLATE_IMPORT = "template_import"
TEMPLATE_EXPORT = "template_export"
TEMPLATE_VERSION_MANAGE = "template_version_manage"

# 任务相关权限
TASK_READ = "task_read"
TASK_CREATE = "task_create"
TASK_UPDATE = "task_update"
TASK_DELETE = "task_delete"
TASK_PUBLISH = "task_publish"

# 报价相关权限
QUOTE_READ = "quote_read"
QUOTE_CREATE = "quote_create"
QUOTE_UPDATE = "quote_update"
QUOTE_DELETE = "quote_delete"

# 用户管理权限
USER_READ = "user_read"
USER_CREATE = "user_create"
USER_UPDATE = "user_update"
USER_DELETE = "user_delete"

# 公司管理权限
COMPANY_READ = "company_read"
COMPANY_CREATE = "company_create"
COMPANY_UPDATE = "company_update"
COMPANY_DELETE = "company_delete"

# 系统配置权限
SYSTEM_CONFIG_READ = "system_config_read"
SYSTEM_CONFIG_UPDATE = "system_config_update"

# 角色权限管理
ROLE_READ = "role_read"
ROLE_CREATE = "role_create"
ROLE_UPDATE = "role_update"
ROLE_DELETE = "role_delete"

PERMISSION_READ = "permission_read"
PERMISSION_CREATE = "permission_create"
PERMISSION_UPDATE = "permission_update"
PERMISSION_DELETE = "permission_delete"

# 权限组定义
BASIC_USER_PERMISSIONS = [
    TASK_READ, TASK_CREATE, TASK_UPDATE,
    QUOTE_READ, QUOTE_CREATE, QUOTE_UPDATE,
    USER_READ,
    COMPANY_READ,
    TEMPLATE_READ, TEMPLATE_CREATE, TEMPLATE_UPDATE, TEMPLATE_EXPORT,
]

DEPARTMENT_MANAGER_PERMISSIONS = BASIC_USER_PERMISSIONS + [
    TASK_DELETE, TASK_PUBLISH,
    QUOTE_DELETE,
    USER_CREATE, USER_UPDATE,
    TEMPLATE_DELETE, TEMPLATE_SHARE, TEMPLATE_VERSION_MANAGE,
]

COMPANY_MANAGER_PERMISSIONS = DEPARTMENT_MANAGER_PERMISSIONS + [
    USER_DELETE,
    COMPANY_UPDATE,
    TEMPLATE_IMPORT,
    SYSTEM_CONFIG_READ,
]

ADMIN_PERMISSIONS = COMPANY_MANAGER_PERMISSIONS + [
    COMPANY_CREATE, COMPANY_DELETE,
    SYSTEM_CONFIG_UPDATE,
    ROLE_READ, ROLE_CREATE, ROLE_UPDATE, ROLE_DELETE,
    PERMISSION_READ, PERMISSION_CREATE, PERMISSION_UPDATE, PERMISSION_DELETE,
]

# 根据用户级别获取权限
def get_permissions_by_level(level: int) -> list:
    """根据用户级别获取权限列表"""
    if level >= 4:  # 总部管理员及以上
        return ADMIN_PERMISSIONS
    elif level >= 3:  # 分公司负责人
        return COMPANY_MANAGER_PERMISSIONS
    elif level >= 2:  # 部门主管
        return DEPARTMENT_MANAGER_PERMISSIONS
    else:  # 普通用户
        return BASIC_USER_PERMISSIONS

# 模板权限检查函数
def can_access_template(user_level: int, template_scope: str, is_creator: bool = False, same_company: bool = False) -> bool:
    """检查用户是否可以访问模板"""
    # 超级管理员和总部管理员可以访问所有模板
    if user_level >= 4:
        return True
    
    # 模板创建者可以访问
    if is_creator:
        return True
    
    # 公开模板所有人都可以访问
    if template_scope == "public":
        return True
    
    # 公司级别模板：同公司用户可以访问
    if template_scope == "company" and same_company:
        return True
    
    # 部门级别模板：分公司负责人和部门主管可以访问
    if template_scope == "department" and same_company and user_level >= 2:
        return True
    
    return False

def can_modify_template(user_level: int, is_creator: bool = False, same_company: bool = False) -> bool:
    """检查用户是否可以修改模板"""
    # 超级管理员和总部管理员可以修改所有模板
    if user_level >= 4:
        return True
    
    # 模板创建者可以修改
    if is_creator:
        return True
    
    # 分公司负责人可以修改本企业模板
    if user_level >= 3 and same_company:
        return True
    
    return False
